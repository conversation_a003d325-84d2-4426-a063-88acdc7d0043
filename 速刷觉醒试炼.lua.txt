-- 保存上次修改的值
lastModifiedValues = {}
watchedResults = {}

-- 记录修改后的值
function recordModifiedValues(key, result)
  lastModifiedValues[key] = {}
  watchedResults[key] = {}
  for i, v in ipairs(result) do
    lastModifiedValues[key][v.address] = v.value
    watchedResults[key][#watchedResults[key] + 1] = v
  end
end

-- 检查值是否发生变化
function hasValuesChanged(key)
  if not watchedResults[key] then return true end
  local current = gg.getValues(watchedResults[key])
  for i, v in ipairs(current) do
    local old = lastModifiedValues[key][v.address]
    if old and old ~= v.value then
      return true
    end
  end
  return false
end

-- 修改函数
function Jay(Search, Get, Type, Range, Name, key)
  gg.clearResults()
  gg.setRanges(Range)
  gg.setVisible(false)

  if Search[1][1] ~= false then
    gg.searchAddress(Search[1][1], 0xFFFFFFFF, Search[1][4] or Type, gg.SIGN_EQUAL, Search[1][5] or 1, Search[1][6] or -1)
  end
  gg.searchNumber(Search[1][2], Search[1][4] or Type, false, gg.SIGN_EQUAL, Search[1][5] or 1, Search[1][6] or -1)
  local count = gg.getResultCount()
  local result = gg.getResults(count)
  gg.clearResults()
  local data = {}
  local base = Search[1][3]

  if count > 0 then
    for i, v in ipairs(result) do v.isUseful = true end
    for k = 2, #Search do
      local tmp = {}
      local offset = Search[k][2] - base
      local num = Search[k][1]
      for i, v in ipairs(result) do
        tmp[#tmp + 1] = {address = v.address + offset, flags = Search[k][3] or Type}
      end
      tmp = gg.getValues(tmp)
      for i, v in ipairs(tmp) do
        local val = (v.flags == 16 or v.flags == 64) and tostring(v.value):sub(1, 6) or v.value
        local target = (v.flags == 16 or v.flags == 64) and tostring(num):sub(1, 6) or num
        if tostring(val) ~= tostring(target) then
          result[i].isUseful = false
        end
      end
    end
    for i, v in ipairs(result) do
      if v.isUseful then data[#data + 1] = v.address end
    end

    if #data > 0 then
      local t, t_ = {}, {}
      for i = 1, #data do
        for k, w in ipairs(Get) do
          local offset = w[2] - base
          if w[1] == false then
            t_[#t_ + 1] = {address = data[i] + offset, flags = Type}
          else
            t[#t + 1] = {address = data[i] + offset, flags = w[3] or Type, value = w[1]}
          end
        end
      end
      gg.setValues(t)
      gg.loadResults(t_)
      gg.clearResults()
      gg.toast(Name .. " 成功！已修改：" .. #data)
      local resultForWatch = gg.getValues(t)
      recordModifiedValues(key, resultForWatch)
      return true
    else
      gg.toast(Name .. " 失败，没有偏移到数据")
      return false
    end
  else
    gg.toast(Name .. " 搜索失败")
    return false
  end
end

-- 所有功能配置
local features = {
  {
    key = "觉醒",
    name = "觉醒试炼速通",
    search = {{false, 7000, 0, 4}, {150, 24, 4}, {75, 28, 4}},
    get = {{200000, 4, 4}, {2222, 20, 4}, {30000, 36, 4}, {13630, 0xc0, 4}},
    type = 4,
    range = 4
  },
  {
    key = "武则天",
    name = "局内加速",
    search = {{false, 13630, 0, 4}, {131072, 88, 4}},
    get = {{256, 128, 4}, {0, 184, 4}, {0, 320, 4}},
    type = 4,
    range = 4
  }
}

-- 记录上次变化时间
local lastChangeTime = {}

-- 主循环，每8秒轮询功能是否需要重新执行
while true do
  local currentTime = os.time()
  for _, f in ipairs(features) do
    if hasValuesChanged(f.key) then
      if not lastChangeTime[f.key] then
        lastChangeTime[f.key] = currentTime  -- 首次变化，记录时间
      end
    end
    -- 如果记录过变化且已经过了2秒，才执行功能
    if lastChangeTime[f.key] and (currentTime - lastChangeTime[f.key] >= 1) then
      Jay(f.search, f.get, f.type, f.range, f.name, f.key)
      lastChangeTime[f.key] = nil  -- 重置，准备下一轮检测
    end
  end
  gg.sleep(5000)
end